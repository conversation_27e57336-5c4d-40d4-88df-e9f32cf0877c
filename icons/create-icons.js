// 图标生成脚本
// 在浏览器中运行此脚本来生成PNG图标

function generateIcon(size) {
    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    const ctx = canvas.getContext('2d');

    // 清除画布
    ctx.clearRect(0, 0, size, size);

    // 绘制圆形背景
    const centerX = size / 2;
    const centerY = size / 2;
    const radius = size * 0.4;

    // 创建渐变
    const gradient = ctx.createLinearGradient(0, 0, size, size);
    gradient.addColorStop(0, '#667eea');
    gradient.addColorStop(1, '#764ba2');

    ctx.fillStyle = gradient;
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    ctx.fill();

    // 绘制问号
    ctx.fillStyle = 'white';
    ctx.font = `bold ${size * 0.3}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('?', centerX, centerY);

    return canvas.toDataURL('image/png');
}

// 生成不同尺寸的图标
console.log('16x16 图标:', generateIcon(16));
console.log('48x48 图标:', generateIcon(48));
console.log('128x128 图标:', generateIcon(128));