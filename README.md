# 智能问答助手 Chrome 扩展

一个功能强大的Chrome浏览器扩展，提供悬浮球和侧边栏功能，内嵌指定的H5页面进行智能问答。

## 🚀 功能特性

### 核心功能
- **悬浮球组件**: 固定在页面右下角，具有hover效果和点击反馈
- **侧边栏**: 点击悬浮球后从右侧滑入，宽度400px
- **H5页面内嵌**: 通过iframe加载指定的语雀文档页面
- **样式隔离**: 使用Shadow DOM确保不与网页样式冲突
- **响应式设计**: 适配不同屏幕尺寸和网站布局

### 交互功能
- 点击悬浮球打开/关闭侧边栏
- 点击遮罩层关闭侧边栏
- 按ESC键快速关闭侧边栏
- 悬浮球hover动画效果
- 侧边栏滑入/滑出动画

## 📦 安装方法

### 开发者模式安装
1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择本项目文件夹
6. 扩展安装完成

### 打包安装
1. 在扩展管理页面点击"打包扩展程序"
2. 选择本项目文件夹
3. 生成.crx文件
4. 拖拽.crx文件到扩展管理页面安装

## 🛠️ 项目结构

```
chrome-extension-qa/
├── manifest.json              # 扩展配置文件
├── content/
│   ├── content-script.js      # 主要功能逻辑
│   └── styles.css             # 样式重置文件
├── background/
│   └── background.js          # 后台服务脚本
├── popup/
│   ├── popup.html             # 扩展弹窗页面
│   ├── popup.css              # 弹窗样式
│   └── popup.js               # 弹窗逻辑
├── icons/
│   ├── icon16.png             # 16x16图标
│   ├── icon48.png             # 48x48图标
│   ├── icon128.png            # 128x128图标
│   └── icon.svg               # 矢量图标
├── create-icons.html          # 图标生成工具
└── README.md                  # 说明文档
```

## ⚙️ 技术实现

### 核心技术
- **Manifest V3**: 使用最新的Chrome扩展规范
- **Content Scripts**: 注入到所有网页中
- **Shadow DOM**: 实现样式隔离
- **Service Worker**: 后台服务处理
- **iframe**: 安全加载外部H5页面

### 关键特性
- **跨域支持**: 配置适当的权限加载外部页面
- **性能优化**: 懒加载和事件防抖
- **兼容性**: 支持各种网站和CSS框架
- **安全性**: CSP策略和权限最小化

## 🎯 使用说明

### 基本使用
1. 安装扩展后，在任意网页都会看到右下角的悬浮球
2. 点击悬浮球打开侧边栏
3. 侧边栏中会加载指定的H5页面
4. 可以在H5页面中进行问答交互
5. 点击关闭按钮或按ESC键关闭侧边栏

### 快捷操作
- **打开侧边栏**: 点击悬浮球
- **关闭侧边栏**: 点击关闭按钮、点击遮罩层、按ESC键
- **扩展设置**: 点击扩展图标打开弹窗

## 🔧 配置说明

### H5页面地址
当前配置的H5页面地址为：
```
https://sqbxszc.yuque.com/bhflbe/ch0gbc/fsmeowuy4im3wb6q#iAH0D
```

如需修改，请编辑 `content/content-script.js` 文件中的 `CONFIG.H5_URL` 配置项。

### 样式自定义
- 悬浮球大小: 修改 `CONFIG.FLOATING_BALL_SIZE`
- 侧边栏宽度: 修改 `CONFIG.SIDEBAR_WIDTH`
- 动画时长: 修改 `CONFIG.ANIMATION_DURATION`

## 🐛 常见问题

### Q: 悬浮球不显示
A: 检查扩展是否正确安装并启用，刷新页面重试。

### Q: 侧边栏无法打开
A: 检查浏览器控制台是否有错误信息，确认网站没有阻止扩展运行。

### Q: H5页面加载失败
A: 检查网络连接，确认目标页面支持iframe嵌入。

### Q: 样式显示异常
A: 某些网站可能有特殊的CSS规则，可以尝试刷新页面或重新安装扩展。

## 📝 开发说明

### 本地开发
1. 克隆项目到本地
2. 修改代码后重新加载扩展
3. 在Chrome扩展管理页面点击刷新按钮

### 调试方法
- 右键悬浮球选择"检查"查看Shadow DOM
- 在控制台查看扩展日志信息
- 使用Chrome开发者工具调试

### 代码规范
- 使用ES6+语法
- 遵循Chrome扩展最佳实践
- 注重性能和安全性

## 📄 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱: <EMAIL>
- GitHub: https://github.com/your-repo/chrome-extension-qa-assistant

---

**注意**: 本扩展仅供学习和研究使用，请遵守相关网站的使用条款。
