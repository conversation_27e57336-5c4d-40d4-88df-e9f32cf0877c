// Chrome扩展 - 智能问答助手后台脚本
// Service Worker for Manifest V3

// 扩展安装时的处理
chrome.runtime.onInstalled.addListener((details) => {
    console.log('智能问答助手扩展已安装', details);
    
    // 设置默认配置
    chrome.storage.sync.set({
        enabled: true,
        position: 'bottom-right',
        theme: 'default'
    });
});

// 扩展启动时的处理
chrome.runtime.onStartup.addListener(() => {
    console.log('智能问答助手扩展已启动');
});

// 处理来自content script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('收到消息:', request);
    
    switch (request.action) {
        case 'getConfig':
            // 获取配置信息
            chrome.storage.sync.get(['enabled', 'position', 'theme'], (result) => {
                sendResponse(result);
            });
            return true; // 保持消息通道开放
            
        case 'saveConfig':
            // 保存配置信息
            chrome.storage.sync.set(request.config, () => {
                sendResponse({ success: true });
            });
            return true;
            
        case 'openSidebar':
            // 记录侧边栏打开事件
            console.log('侧边栏已打开');
            break;
            
        case 'closeSidebar':
            // 记录侧边栏关闭事件
            console.log('侧边栏已关闭');
            break;
            
        default:
            console.log('未知消息类型:', request.action);
    }
});

// 标签页更新时的处理
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url) {
        // 页面加载完成，可以进行一些初始化操作
        console.log('页面加载完成:', tab.url);
    }
});

// 处理扩展图标点击事件
chrome.action.onClicked.addListener((tab) => {
    // 向当前标签页发送消息
    chrome.tabs.sendMessage(tab.id, {
        action: 'toggleSidebar'
    });
});
