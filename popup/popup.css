/* Chrome扩展 - 弹窗样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 300px;
    min-height: 400px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: #f5f5f5;
}

.popup-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    text-align: center;
}

.header h1 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 5px;
}

.version {
    font-size: 12px;
    opacity: 0.8;
}

.content {
    flex: 1;
    padding: 20px;
}

.status {
    margin-bottom: 20px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-label {
    font-weight: 500;
    color: #333;
}

.status-value {
    color: #28a745;
    font-weight: 600;
}

.actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
}

.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
}

.btn-secondary {
    background: white;
    color: #667eea;
    border: 1px solid #667eea;
}

.btn-secondary:hover {
    background: #667eea;
    color: white;
}

.info {
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.info p {
    font-size: 13px;
    color: #666;
    line-height: 1.5;
    margin-bottom: 8px;
}

.info p:last-child {
    margin-bottom: 0;
}

.footer {
    padding: 15px 20px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: space-between;
}

.footer a {
    font-size: 12px;
    color: #667eea;
    text-decoration: none;
}

.footer a:hover {
    text-decoration: underline;
}
