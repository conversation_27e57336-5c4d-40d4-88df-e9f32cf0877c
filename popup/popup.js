// Chrome扩展 - 弹窗脚本

document.addEventListener('DOMContentLoaded', function() {
    const toggleBtn = document.getElementById('toggleBtn');
    const settingsBtn = document.getElementById('settingsBtn');
    const statusValue = document.getElementById('status');
    const helpLink = document.getElementById('helpLink');
    const feedbackLink = document.getElementById('feedbackLink');
    
    // 获取当前状态
    function updateStatus() {
        chrome.storage.sync.get(['enabled'], function(result) {
            const enabled = result.enabled !== false; // 默认启用
            statusValue.textContent = enabled ? '已启用' : '已禁用';
            statusValue.style.color = enabled ? '#28a745' : '#dc3545';
            toggleBtn.textContent = enabled ? '禁用扩展' : '启用扩展';
        });
    }
    
    // 切换扩展状态
    toggleBtn.addEventListener('click', function() {
        chrome.storage.sync.get(['enabled'], function(result) {
            const currentEnabled = result.enabled !== false;
            const newEnabled = !currentEnabled;
            
            chrome.storage.sync.set({ enabled: newEnabled }, function() {
                updateStatus();
                
                // 通知所有标签页更新状态
                chrome.tabs.query({}, function(tabs) {
                    tabs.forEach(tab => {
                        chrome.tabs.sendMessage(tab.id, {
                            action: 'updateStatus',
                            enabled: newEnabled
                        }).catch(() => {
                            // 忽略无法发送消息的标签页
                        });
                    });
                });
            });
        });
    });
    
    // 设置按钮
    settingsBtn.addEventListener('click', function() {
        chrome.tabs.create({
            url: chrome.runtime.getURL('options/options.html')
        });
    });
    
    // 帮助链接
    helpLink.addEventListener('click', function(e) {
        e.preventDefault();
        chrome.tabs.create({
            url: 'https://github.com/your-repo/chrome-extension-qa-assistant'
        });
    });
    
    // 反馈链接
    feedbackLink.addEventListener('click', function(e) {
        e.preventDefault();
        chrome.tabs.create({
            url: 'mailto:<EMAIL>?subject=智能问答助手反馈'
        });
    });
    
    // 初始化状态
    updateStatus();
    
    // 监听存储变化
    chrome.storage.onChanged.addListener(function(changes, namespace) {
        if (namespace === 'sync' && changes.enabled) {
            updateStatus();
        }
    });
});
