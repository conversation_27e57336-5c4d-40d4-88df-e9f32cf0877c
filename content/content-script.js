// Chrome扩展 - 智能问答助手
// 悬浮球和侧边栏功能实现

(function() {
    'use strict';
    
    // 防止重复注入
    if (window.qaAssistantInjected) {
        return;
    }
    window.qaAssistantInjected = true;
    
    // 配置常量
    const CONFIG = {
        H5_URL: 'https://sqbxszc.yuque.com/bhflbe/ch0gbc/fsmeowuy4im3wb6q#iAH0D',
        FLOATING_BALL_SIZE: 60,
        SIDEBAR_WIDTH: 400,
        ANIMATION_DURATION: 300
    };
    
    // 全局变量
    let floatingBall = null;
    let sidebar = null;
    let isOpen = false;
    let shadowRoot = null;
    
    // 创建Shadow DOM容器
    function createShadowContainer() {
        const container = document.createElement('div');
        container.id = 'qa-assistant-container';
        container.style.cssText = `
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            pointer-events: none !important;
            z-index: 2147483647 !important;
        `;
        
        shadowRoot = container.attachShadow({ mode: 'closed' });
        document.documentElement.appendChild(container);
        
        return shadowRoot;
    }
    
    // 创建样式
    function createStyles() {
        const style = document.createElement('style');
        style.textContent = `
            /* 重置样式 */
            * {
                box-sizing: border-box;
                margin: 0;
                padding: 0;
            }
            
            /* 悬浮球样式 */
            .floating-ball {
                position: fixed !important;
                bottom: 30px !important;
                right: 30px !important;
                width: ${CONFIG.FLOATING_BALL_SIZE}px !important;
                height: ${CONFIG.FLOATING_BALL_SIZE}px !important;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                border-radius: 50% !important;
                cursor: pointer !important;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                transition: all 0.3s ease !important;
                pointer-events: auto !important;
                z-index: 2147483647 !important;
                user-select: none !important;
            }
            
            .floating-ball:hover {
                transform: scale(1.1) !important;
                box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4) !important;
            }
            
            .floating-ball:active {
                transform: scale(0.95) !important;
            }
            
            .floating-ball-icon {
                width: 30px !important;
                height: 30px !important;
                background: white !important;
                border-radius: 50% !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                font-size: 18px !important;
                color: #667eea !important;
                font-weight: bold !important;
            }
            
            /* 侧边栏样式 */
            .sidebar {
                position: fixed !important;
                top: 0 !important;
                right: -${CONFIG.SIDEBAR_WIDTH}px !important;
                width: ${CONFIG.SIDEBAR_WIDTH}px !important;
                height: 100vh !important;
                background: white !important;
                box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1) !important;
                transition: right ${CONFIG.ANIMATION_DURATION}ms ease-in-out !important;
                z-index: 2147483646 !important;
                pointer-events: auto !important;
                display: flex !important;
                flex-direction: column !important;
            }
            
            .sidebar.open {
                right: 0 !important;
            }
            
            .sidebar-header {
                height: 50px !important;
                background: #667eea !important;
                color: white !important;
                display: flex !important;
                align-items: center !important;
                justify-content: space-between !important;
                padding: 0 15px !important;
                font-size: 16px !important;
                font-weight: bold !important;
            }
            
            .close-btn {
                width: 30px !important;
                height: 30px !important;
                border: none !important;
                background: rgba(255, 255, 255, 0.2) !important;
                color: white !important;
                border-radius: 50% !important;
                cursor: pointer !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                font-size: 18px !important;
                transition: background 0.2s ease !important;
            }
            
            .close-btn:hover {
                background: rgba(255, 255, 255, 0.3) !important;
            }
            
            .sidebar-content {
                flex: 1 !important;
                overflow: hidden !important;
            }
            
            .sidebar-iframe {
                width: 100% !important;
                height: 100% !important;
                border: none !important;
                background: white !important;
            }
            
            /* 遮罩层 */
            .overlay {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 100% !important;
                height: 100% !important;
                background: rgba(0, 0, 0, 0.3) !important;
                opacity: 0 !important;
                visibility: hidden !important;
                transition: all ${CONFIG.ANIMATION_DURATION}ms ease-in-out !important;
                z-index: 2147483645 !important;
                pointer-events: auto !important;
            }
            
            .overlay.show {
                opacity: 1 !important;
                visibility: visible !important;
            }
        `;
        return style;
    }
    
    // 创建悬浮球
    function createFloatingBall() {
        floatingBall = document.createElement('div');
        floatingBall.className = 'floating-ball';
        floatingBall.innerHTML = '<div class="floating-ball-icon">?</div>';
        floatingBall.title = '点击打开智能问答助手';
        
        floatingBall.addEventListener('click', toggleSidebar);
        
        return floatingBall;
    }
    
    // 创建侧边栏
    function createSidebar() {
        // 遮罩层
        const overlay = document.createElement('div');
        overlay.className = 'overlay';
        overlay.addEventListener('click', closeSidebar);
        
        // 侧边栏容器
        sidebar = document.createElement('div');
        sidebar.className = 'sidebar';
        
        // 侧边栏头部
        const header = document.createElement('div');
        header.className = 'sidebar-header';
        header.innerHTML = `
            <span>智能问答助手</span>
            <button class="close-btn" title="关闭">×</button>
        `;
        
        // 关闭按钮事件
        const closeBtn = header.querySelector('.close-btn');
        closeBtn.addEventListener('click', closeSidebar);
        
        // 侧边栏内容
        const content = document.createElement('div');
        content.className = 'sidebar-content';
        
        // iframe
        const iframe = document.createElement('iframe');
        iframe.className = 'sidebar-iframe';
        iframe.src = CONFIG.H5_URL;
        iframe.allow = 'fullscreen';
        iframe.loading = 'lazy';
        
        content.appendChild(iframe);
        sidebar.appendChild(header);
        sidebar.appendChild(content);
        
        return { overlay, sidebar };
    }
    
    // 切换侧边栏显示状态
    function toggleSidebar() {
        if (isOpen) {
            closeSidebar();
        } else {
            openSidebar();
        }
    }
    
    // 打开侧边栏
    function openSidebar() {
        if (isOpen) return;
        
        isOpen = true;
        const overlay = shadowRoot.querySelector('.overlay');
        const sidebar = shadowRoot.querySelector('.sidebar');
        
        overlay.classList.add('show');
        sidebar.classList.add('open');
        
        // 防止页面滚动
        document.body.style.overflow = 'hidden';
    }
    
    // 关闭侧边栏
    function closeSidebar() {
        if (!isOpen) return;
        
        isOpen = false;
        const overlay = shadowRoot.querySelector('.overlay');
        const sidebar = shadowRoot.querySelector('.sidebar');
        
        overlay.classList.remove('show');
        sidebar.classList.remove('open');
        
        // 恢复页面滚动
        document.body.style.overflow = '';
    }
    
    // 键盘事件处理
    function handleKeydown(event) {
        if (event.key === 'Escape' && isOpen) {
            closeSidebar();
        }
    }
    
    // 初始化扩展
    function init() {
        try {
            // 创建Shadow DOM容器
            const shadow = createShadowContainer();
            
            // 添加样式
            shadow.appendChild(createStyles());
            
            // 创建悬浮球
            shadow.appendChild(createFloatingBall());
            
            // 创建侧边栏
            const { overlay, sidebar } = createSidebar();
            shadow.appendChild(overlay);
            shadow.appendChild(sidebar);
            
            // 添加键盘事件监听
            document.addEventListener('keydown', handleKeydown);
            
            console.log('智能问答助手扩展已加载');
        } catch (error) {
            console.error('智能问答助手扩展加载失败:', error);
        }
    }
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
})();
