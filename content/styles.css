/* Chrome扩展 - 智能问答助手样式文件 */
/* 这个文件主要用于确保扩展样式不被页面CSS影响 */

/* 全局重置，防止页面样式影响扩展 */
#qa-assistant-container {
    all: initial !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    pointer-events: none !important;
    z-index: 2147483647 !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
}

/* 确保扩展元素不受页面CSS影响 */
#qa-assistant-container * {
    all: unset !important;
    box-sizing: border-box !important;
}

/* 防止页面样式覆盖 */
#qa-assistant-container div,
#qa-assistant-container button,
#qa-assistant-container iframe {
    display: block !important;
    position: static !important;
    margin: 0 !important;
    padding: 0 !important;
    border: 0 !important;
    outline: 0 !important;
    background: transparent !important;
    color: inherit !important;
    font: inherit !important;
    text-align: left !important;
    text-decoration: none !important;
    list-style: none !important;
    vertical-align: baseline !important;
}
