<!DOCTYPE html>
<html>
<head>
    <title>生成扩展图标</title>
</head>
<body>
    <canvas id="canvas16" width="16" height="16" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    <canvas id="canvas48" width="48" height="48" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    <canvas id="canvas128" width="128" height="128" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    
    <div>
        <button onclick="downloadIcons()">下载图标</button>
    </div>
    
    <script>
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const centerX = size / 2;
            const centerY = size / 2;
            const radius = size * 0.4;
            
            // 清除画布
            ctx.clearRect(0, 0, size, size);
            
            // 绘制渐变背景
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.fill();
            
            // 绘制问号
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size * 0.5}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('?', centerX, centerY);
        }
        
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        function downloadIcons() {
            downloadCanvas(document.getElementById('canvas16'), 'icon16.png');
            downloadCanvas(document.getElementById('canvas48'), 'icon48.png');
            downloadCanvas(document.getElementById('canvas128'), 'icon128.png');
        }
        
        // 绘制所有尺寸的图标
        drawIcon(document.getElementById('canvas16'), 16);
        drawIcon(document.getElementById('canvas48'), 48);
        drawIcon(document.getElementById('canvas128'), 128);
    </script>
</body>
</html>
