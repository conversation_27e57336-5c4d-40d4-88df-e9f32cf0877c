<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chrome扩展测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .test-section h2 {
            color: #667eea;
            margin-top: 0;
        }
        
        .test-item {
            margin: 15px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
        }
        
        .status {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status.pending {
            background: #ffeaa7;
            color: #d63031;
        }
        
        .status.success {
            background: #55efc4;
            color: #00b894;
        }
        
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #5a6fd8;
        }
        
        .content-area {
            min-height: 500px;
            padding: 20px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 智能问答助手扩展测试页面</h1>
        
        <div class="instructions">
            <h3>📋 测试说明</h3>
            <p>本页面用于测试Chrome扩展"智能问答助手"的各项功能。请按照以下步骤进行测试：</p>
            <ol>
                <li>确保扩展已正确安装并启用</li>
                <li>刷新本页面</li>
                <li>查看页面右下角是否出现悬浮球</li>
                <li>按照测试项目逐一验证功能</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h2>🎯 基础功能测试</h2>
            
            <div class="test-item">
                <strong>1. 悬浮球显示</strong>
                <span class="status pending" id="status1">待测试</span>
                <p>检查页面右下角是否显示紫色渐变的圆形悬浮球</p>
                <button onclick="checkFloatingBall()">检查悬浮球</button>
            </div>
            
            <div class="test-item">
                <strong>2. 悬浮效果</strong>
                <span class="status pending" id="status2">待测试</span>
                <p>鼠标悬停在悬浮球上时，应有放大动画效果</p>
                <button onclick="testHoverEffect()">测试悬停效果</button>
            </div>
            
            <div class="test-item">
                <strong>3. 侧边栏打开</strong>
                <span class="status pending" id="status3">待测试</span>
                <p>点击悬浮球应从页面右侧滑入侧边栏</p>
                <button onclick="testSidebarOpen()">测试侧边栏</button>
            </div>
            
            <div class="test-item">
                <strong>4. H5页面加载</strong>
                <span class="status pending" id="status4">待测试</span>
                <p>侧边栏中应正确加载语雀文档页面</p>
                <button onclick="testIframeLoad()">检查页面加载</button>
            </div>
            
            <div class="test-item">
                <strong>5. 关闭功能</strong>
                <span class="status pending" id="status5">待测试</span>
                <p>按ESC键或点击遮罩层应能关闭侧边栏</p>
                <button onclick="testCloseFunction()">测试关闭功能</button>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔧 兼容性测试</h2>
            
            <div class="test-item">
                <strong>6. 样式隔离</strong>
                <span class="status pending" id="status6">待测试</span>
                <p>扩展UI不应受到页面CSS样式影响</p>
                <button onclick="testStyleIsolation()">测试样式隔离</button>
            </div>
            
            <div class="test-item">
                <strong>7. 页面交互</strong>
                <span class="status pending" id="status7">待测试</span>
                <p>扩展不应影响页面正常功能</p>
                <button onclick="testPageInteraction()">测试页面交互</button>
            </div>
        </div>
        
        <div class="note">
            <strong>💡 提示：</strong>
            <ul>
                <li>如果某项测试失败，请检查浏览器控制台的错误信息</li>
                <li>确保扩展在chrome://extensions/页面中处于启用状态</li>
                <li>某些功能可能需要刷新页面后才能正常工作</li>
            </ul>
        </div>
        
        <div class="content-area">
            <h3>📝 测试内容区域</h3>
            <p>这是一个内容区域，用于测试扩展是否会影响页面正常显示和交互。</p>
            <p>您可以在这里进行各种操作：</p>
            <ul>
                <li>滚动页面测试悬浮球位置是否固定</li>
                <li>选择文本测试是否正常</li>
                <li>点击链接测试页面交互</li>
            </ul>
            
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
            
            <button onclick="alert('页面交互正常')">测试按钮</button>
            <input type="text" placeholder="测试输入框" style="margin: 10px; padding: 5px;">
            
            <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
        </div>
    </div>
    
    <script>
        // 测试脚本
        function updateStatus(id, success, message) {
            const element = document.getElementById(id);
            element.className = success ? 'status success' : 'status pending';
            element.textContent = success ? '✅ 通过' : '❌ 失败';
            if (message) {
                alert(message);
            }
        }
        
        function checkFloatingBall() {
            // 检查是否存在悬浮球
            const hasFloatingBall = document.querySelector('#qa-assistant-container');
            updateStatus('status1', !!hasFloatingBall, 
                hasFloatingBall ? '悬浮球检测成功！' : '未检测到悬浮球，请检查扩展是否正确安装。');
        }
        
        function testHoverEffect() {
            alert('请将鼠标悬停在右下角的悬浮球上，观察是否有放大动画效果。');
            setTimeout(() => updateStatus('status2', true), 1000);
        }
        
        function testSidebarOpen() {
            alert('请点击右下角的悬浮球，观察是否从右侧滑入侧边栏。');
            setTimeout(() => updateStatus('status3', true), 1000);
        }
        
        function testIframeLoad() {
            alert('请打开侧边栏，检查是否正确加载了语雀文档页面。');
            setTimeout(() => updateStatus('status4', true), 1000);
        }
        
        function testCloseFunction() {
            alert('请打开侧边栏后，尝试按ESC键或点击遮罩层关闭侧边栏。');
            setTimeout(() => updateStatus('status5', true), 1000);
        }
        
        function testStyleIsolation() {
            // 添加一些可能冲突的样式
            const style = document.createElement('style');
            style.textContent = `
                * { background: red !important; }
                div { border: 5px solid blue !important; }
            `;
            document.head.appendChild(style);
            
            setTimeout(() => {
                document.head.removeChild(style);
                updateStatus('status6', true, '样式隔离测试完成，扩展UI应该不受影响。');
            }, 2000);
        }
        
        function testPageInteraction() {
            updateStatus('status7', true, '页面交互测试通过！扩展没有影响页面正常功能。');
        }
        
        // 页面加载完成后的自动检查
        window.addEventListener('load', function() {
            setTimeout(checkFloatingBall, 1000);
        });
    </script>
</body>
</html>
