# 智能问答助手 Chrome 扩展 - 安装指南

## 📋 安装前准备

### 系统要求
- Chrome 浏览器版本 88 或更高
- 支持 Manifest V3 的浏览器
- 网络连接正常

### 文件检查
确保以下文件存在：
```
chrome-extension-qa/
├── manifest.json
├── content/
│   ├── content-script.js
│   └── styles.css
├── background/
│   └── background.js
├── popup/
│   ├── popup.html
│   ├── popup.css
│   └── popup.js
└── icons/
    └── icon.svg
```

## 🚀 安装步骤

### 方法一：开发者模式安装（推荐）

1. **打开Chrome扩展管理页面**
   - 在地址栏输入：`chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

2. **启用开发者模式**
   - 点击页面右上角的"开发者模式"开关
   - 确保开关处于开启状态

3. **加载扩展**
   - 点击"加载已解压的扩展程序"按钮
   - 选择项目文件夹（包含manifest.json的文件夹）
   - 点击"选择文件夹"

4. **验证安装**
   - 扩展列表中应该出现"智能问答助手"
   - 确保扩展处于启用状态
   - 浏览器工具栏可能出现扩展图标

### 方法二：打包安装

1. **打包扩展**
   - 在扩展管理页面点击"打包扩展程序"
   - 选择项目根目录
   - 生成 .crx 和 .pem 文件

2. **安装打包文件**
   - 将 .crx 文件拖拽到扩展管理页面
   - 点击"添加扩展程序"确认安装

## ✅ 安装验证

### 检查扩展状态
1. 访问任意网页（如：https://www.baidu.com）
2. 查看页面右下角是否出现悬浮球
3. 点击悬浮球测试侧边栏功能

### 功能测试
- **悬浮球显示**: 右下角应显示紫色渐变圆形按钮
- **悬浮效果**: 鼠标悬停时按钮应有放大效果
- **侧边栏打开**: 点击悬浮球应从右侧滑入侧边栏
- **H5页面加载**: 侧边栏中应正确加载语雀文档页面
- **关闭功能**: ESC键或点击遮罩应能关闭侧边栏

## 🔧 常见安装问题

### 问题1：扩展无法加载
**症状**: 点击"加载已解压的扩展程序"后出现错误
**解决方案**:
- 检查manifest.json文件格式是否正确
- 确保所有引用的文件都存在
- 查看错误信息并修复相应问题

### 问题2：悬浮球不显示
**症状**: 安装成功但页面上看不到悬浮球
**解决方案**:
- 刷新页面重试
- 检查扩展是否启用
- 查看浏览器控制台是否有错误信息
- 确认网站没有阻止扩展运行

### 问题3：侧边栏无法打开
**症状**: 点击悬浮球没有反应
**解决方案**:
- 检查浏览器控制台错误信息
- 确认网络连接正常
- 重新加载扩展

### 问题4：H5页面加载失败
**症状**: 侧边栏打开但内容为空或显示错误
**解决方案**:
- 检查网络连接
- 确认目标URL可以正常访问
- 检查是否被防火墙或广告拦截器阻止

## 🛠️ 开发调试

### 调试模式
1. 右键点击悬浮球选择"检查"
2. 在开发者工具中查看Shadow DOM
3. 查看Console面板的日志信息

### 重新加载扩展
修改代码后需要重新加载：
1. 在扩展管理页面找到"智能问答助手"
2. 点击刷新按钮（🔄）
3. 刷新测试页面

### 查看日志
- 扩展控制台：右键扩展图标 → 检查弹出内容
- 后台脚本：扩展详情页 → 检查视图 → 背景页
- 内容脚本：页面开发者工具 → Console

## 📱 移动端支持

### Chrome Mobile
- Android Chrome 浏览器支持部分扩展功能
- 悬浮球可能需要手动启用
- 建议在桌面版Chrome中使用

## 🔒 权限说明

### 必需权限
- `activeTab`: 访问当前活动标签页
- `storage`: 保存扩展设置
- `host_permissions`: 访问所有网站以注入内容脚本

### 安全提示
- 扩展只在用户主动点击时才会显示侧边栏
- 不会收集或传输用户数据
- 所有操作都在本地进行

## 📞 技术支持

如果遇到安装问题，请：
1. 查看浏览器控制台错误信息
2. 检查扩展管理页面的错误提示
3. 参考README.md中的常见问题解答
4. 联系技术支持：<EMAIL>

---

**提示**: 首次安装建议在简单的网页（如百度首页）上测试功能，确认正常后再在复杂网站上使用。
